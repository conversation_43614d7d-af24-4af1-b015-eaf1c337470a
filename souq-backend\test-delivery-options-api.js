const mongoose = require('mongoose');
const DeliveryOption = require('./db/models/deliveryOptionModel');
const ShippingProvider = require('./db/models/shippingProviderModel');
const User = require('./db/models/userModel');

async function testDeliveryOptionsAPI() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/souq');
    console.log('✅ Connected to MongoDB');

    // Check if we have any users
    const users = await User.find().limit(5);
    console.log(`\n📊 Found ${users.length} users in database`);
    
    if (users.length > 0) {
      const testUser = users[0];
      console.log(`🔍 Testing with user: ${testUser.email} (ID: ${testUser._id})`);
      
      // Check delivery options for this user
      const deliveryOptions = await DeliveryOption.find({ user: testUser._id })
        .populate('shippingProvider')
        .populate('user', 'email');
      
      console.log(`\n📦 Found ${deliveryOptions.length} delivery options for user ${testUser.email}`);
      
      if (deliveryOptions.length > 0) {
        deliveryOptions.forEach((option, index) => {
          console.log(`\n${index + 1}. Delivery Option:`);
          console.log(`   ID: ${option._id}`);
          console.log(`   Service: ${option.serviceName} (${option.serviceCode})`);
          console.log(`   Provider: ${option.shippingProvider?.displayName || 'No provider'}`);
          console.log(`   Provider Name: ${option.shippingProvider?.name || 'No provider name'}`);
          console.log(`   Is Default: ${option.isDefault}`);
          console.log(`   Is Active: ${option.isActive}`);
          console.log(`   Provider Active: ${option.shippingProvider?.isActive || 'N/A'}`);
        });
      } else {
        console.log('❌ No delivery options found for this user');
        
        // Check if we have any shipping providers
        const providers = await ShippingProvider.find({ isActive: true });
        console.log(`\n📋 Found ${providers.length} active shipping providers:`);
        providers.forEach(provider => {
          console.log(`   - ${provider.displayName} (${provider.name})`);
        });
        
        if (providers.length > 0) {
          console.log('\n🔧 Creating a test delivery option...');
          const testProvider = providers[0];
          
          const newDeliveryOption = await DeliveryOption.create({
            user: testUser._id,
            shippingProvider: testProvider._id,
            serviceCode: 'LOCAL_PICKUP',
            serviceName: 'Local Pickup',
            isDefault: true,
            isActive: true
          });
          
          console.log(`✅ Created test delivery option: ${newDeliveryOption._id}`);
        }
      }
    } else {
      console.log('❌ No users found in database');
    }

    // Check all delivery options in database
    const allDeliveryOptions = await DeliveryOption.find()
      .populate('shippingProvider')
      .populate('user', 'email');
    
    console.log(`\n📊 Total delivery options in database: ${allDeliveryOptions.length}`);
    
    if (allDeliveryOptions.length > 0) {
      console.log('\nAll delivery options:');
      allDeliveryOptions.forEach((option, index) => {
        console.log(`${index + 1}. ${option.user?.email || 'No user'} - ${option.serviceName} (${option.shippingProvider?.displayName || 'No provider'})`);
      });
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the test
testDeliveryOptionsAPI();
