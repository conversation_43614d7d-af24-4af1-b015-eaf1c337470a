const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const User = require('./db/models/userModel');
const DeliveryOption = require('./db/models/deliveryOptionModel');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Simple auth middleware for testing
const testAuth = async (req, res, next) => {
  try {
    // For testing, let's use the first user in the database
    const user = await User.findOne({ email: '<EMAIL>' });
    if (!user) {
      return res.status(401).json({ success: false, error: 'User not found' });
    }
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ success: false, error: 'Authentication failed' });
  }
};

// Test endpoint for delivery options
app.get('/test/delivery-options', testAuth, async (req, res) => {
  try {
    console.log('🔍 Testing delivery options endpoint...');
    console.log('User:', req.user.email);
    
    const userId = req.user._id;
    console.log('User ID:', userId);

    const deliveryOptions = await DeliveryOption.find({ user: userId, isActive: true })
      .populate({
        path: 'shippingProvider',
        select: 'name displayName supportedServices isActive pricing',
        match: { isActive: true }
      })
      .sort({ isDefault: -1, createdAt: -1 });

    console.log(`Found ${deliveryOptions.length} delivery options before filtering`);
    
    // Filter out delivery options where shipping provider is inactive or null
    const activeDeliveryOptions = deliveryOptions.filter(option => {
      const hasProvider = option.shippingProvider && option.shippingProvider.isActive;
      console.log(`Option ${option._id}: Provider=${option.shippingProvider?.displayName}, Active=${hasProvider}`);
      return hasProvider;
    });

    console.log(`Found ${activeDeliveryOptions.length} active delivery options after filtering`);
    
    activeDeliveryOptions.forEach((option, index) => {
      console.log(`${index + 1}. ${option.serviceName} - ${option.shippingProvider.displayName}`);
    });

    res.json({
      success: true,
      data: { deliveryOptions: activeDeliveryOptions }
    });
  } catch (error) {
    console.error('Error in test endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch delivery options'
    });
  }
});

// Start test server
async function startTestServer() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/souq');
    console.log('✅ Connected to MongoDB');

    const PORT = 3001;
    app.listen(PORT, () => {
      console.log(`🚀 Test server running on http://localhost:${PORT}`);
      console.log(`📡 Test endpoint: http://localhost:${PORT}/test/delivery-options`);
      console.log('\nPress Ctrl+C to stop the server');
    });
  } catch (error) {
    console.error('❌ Failed to start test server:', error);
    process.exit(1);
  }
}

startTestServer();
